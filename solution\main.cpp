#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX
#include <unordered_map>

using namespace std;

// 缓存机制：减少重复计算
unordered_map<int, int> inference_time_cache;

int get_cached_inference_time(int batch_size, int k) {
    int key = batch_size * 1000 + k; // 简单的键生成
    auto it = inference_time_cache.find(key);
    if (it != inference_time_cache.end()) {
        return it->second;
    }

    int result = 0;
    if (batch_size > 0) {
        double sqrt_b = sqrt(static_cast<double>(batch_size));
        result = static_cast<int>(ceil(sqrt_b / k));
    }

    inference_time_cache[key] = result;
    return result;
}

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;
    int total_tasks = 0; // 负载统计
    long long total_processing_time = 0; // 总处理时间

    void add_task(int start_time, int finish_time, int mem_needed) {
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, { start_time, mem_needed });

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, { finish_time, -mem_needed });

        // 更新负载统计
        total_tasks++;
        total_processing_time += (finish_time - start_time);
    }

    // 计算NPU负载评分（越小越好）
    double get_load_score() const {
        return total_tasks * 10.0 + total_processing_time * 0.01;
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1;
        }

        int current_mem_usage = 0;
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            }
            else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        int future_mem_usage = current_mem_usage;
        for (auto i = it; i != events.end(); ++i) {
            future_mem_usage += i->second;
            if (future_mem_usage + mem_needed <= m) {
                return i->first;
            }
        }
        return -1;
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    vector<tuple<int, int, int, int>> requests;
};


// --- Helper Functions ---

// 修正推理时间计算公式：⌈√Bj/ki⌉ - 使用缓存版本
int calculate_inference_time(int batch_size, int k) {
    return get_cached_inference_time(batch_size, k);
}

// 高效的批次大小选择：基于数学分析的启发式方法
int find_optimal_batch_size(int max_batch_mem, int samples_left, int k, long long remaining_time) {
    if (remaining_time <= 0) {
        return 1;
    }

    int max_possible = min(max_batch_mem, (int)samples_left);

    // 基于推理时间公式 ⌈√B/k⌉ <= remaining_time
    // 理论最大值：B <= (k * remaining_time)^2
    long long theoretical_max_ll = (long long)k * remaining_time;
    theoretical_max_ll = theoretical_max_ll * theoretical_max_ll;
    int theoretical_max = min((long long)max_possible, theoretical_max_ll);

    if (theoretical_max <= 0) return 1;

    // 直接验证理论最大值
    if (calculate_inference_time(theoretical_max, k) <= remaining_time) {
        return theoretical_max;
    }

    // 如果理论值过大，使用二分查找精确定位
    int low = 1, high = theoretical_max;
    int best = 1;

    while (low <= high) {
        int mid = low + (high - low) / 2;
        if (calculate_inference_time(mid, k) <= remaining_time) {
            best = mid;
            low = mid + 1;
        } else {
            high = mid - 1;
        }
    }

    return best;
}


// --- Main Logic ---

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({ npu_counter++, server.id, server.k, server.m, {}, 0, 0 });
        }
    }

    // 预处理：按紧急程度对用户进行初步排序
    vector<int> user_urgency_order(M);
    iota(user_urgency_order.begin(), user_urgency_order.end(), 0);

    sort(user_urgency_order.begin(), user_urgency_order.end(), [&](int a, int b) {
        const User& ua = users[a];
        const User& ub = users[b];

        // 计算紧急程度：样本密度 * 时间窗口紧迫性
        double urgency_a = (double)ua.cnt / (ua.e - ua.s);
        double urgency_b = (double)ub.cnt / (ub.e - ub.s);

        // 时间窗口越短，优先级越高
        if (ua.e - ua.s != ub.e - ub.s) {
            return ua.e - ua.s < ub.e - ub.s;
        }

        return urgency_a > urgency_b;
    });

    // 智能用户调度：优先考虑紧急程度和时间窗口约束
    auto user_priority = [&](int user_id) -> double {
        const User& user = users[user_id];
        double time_window = user.e - user.s;
        double urgency = (double)user.cnt / time_window; // 样本密度
        double remaining_ratio = (double)user.samples_left / user.cnt;
        return urgency * remaining_ratio * 1000.0; // 放大以便整数比较
    };

    auto user_comparator = [&](const pair<long long, int>& a, const pair<long long, int>& b) {
        if (a.first != b.first) return a.first > b.first; // 时间优先
        // 时间相同时，优先级高的先处理
        return user_priority(a.second) < user_priority(b.second);
    };

    priority_queue<pair<long long, int>, vector<pair<long long, int>>, decltype(user_comparator)> user_pq(user_comparator);
    for (const auto& user : users) {
        user_pq.push({ user.next_send_time, user.id });
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        long long time = top.first;
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = max(time, user.next_send_time);

        // 智能NPU选择策略：综合考虑多个因素
        struct NPUCandidate {
            int npu_idx;
            int finish_time;
            int batch_size;
            int latency;
            double score; // 综合评分，越小越好
        };

        vector<NPUCandidate> candidates;

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            long long time_for_inference = max(0LL, (long long)user.e - arrival_time);

            int good_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left, server.k, time_for_inference);
            int mem_needed = A * good_batch + B;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            int inference_time = calculate_inference_time(good_batch, server.k);
            int finish_time = start_time + inference_time;

            // 计算综合评分（基于数学分析的权重分配）
            double score = 0.0;

            // 1. 超时惩罚（最重要，指数级影响）
            if (finish_time > user.e) {
                double timeout_ratio = (double)(finish_time - user.e) / (user.e - user.s);
                score += 10000.0 * timeout_ratio; // 极高权重
            }

            // 2. 完成时间惩罚（影响超时比例）
            if (finish_time <= user.e) {
                double completion_ratio = (double)(finish_time - user.e) / (user.e - user.s);
                score += 100.0 * abs(completion_ratio); // 鼓励提前完成
            }

            // 3. 迁移惩罚（相对较小但仍需考虑）
            if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                score += 25.0; // 基于p(x)=2^(-x/200)的权重
            }

            // 4. 批次效率奖励（鼓励大批次以减少请求数）
            score -= 0.1 * good_batch;

            // 5. 通信延迟惩罚
            score += 0.2 * latency;

            // 6. NPU负载均衡（新增）
            score += 0.05 * npu.get_load_score();

            candidates.push_back({i, finish_time, good_batch, latency, score});
        }

        // 选择最佳候选
        int best_npu_idx = -1;
        int best_finish_time = -1;
        int best_batch_size = -1;
        int best_latency = -1;

        if (!candidates.empty()) {
            auto best_candidate = *min_element(candidates.begin(), candidates.end(),
                [](const NPUCandidate& a, const NPUCandidate& b) {
                    return a.score < b.score;
                });

            best_npu_idx = best_candidate.npu_idx;
            best_finish_time = best_candidate.finish_time;
            best_batch_size = best_candidate.batch_size;
            best_latency = best_candidate.latency;
        }

        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k);
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for (int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            // 优化的下次发送时间计算：考虑通信延迟和处理效率
            user.next_send_time = send_time + best_latency + 1;
            user.last_npu_id = chosen_npu.id;

            // 动态调整发送间隔：如果用户时间紧迫，尽快发送下一个请求
            if (user.samples_left > 0) {
                long long remaining_time = user.e - user.next_send_time;
                long long remaining_samples = user.samples_left;

                // 如果时间非常紧迫，可以考虑并行发送到不同NPU
                if (remaining_time > 0 && remaining_samples > 0) {
                    double samples_per_ms = (double)remaining_samples / remaining_time;
                    if (samples_per_ms > 1.0) {
                        // 时间紧迫，优先级提升
                        user_pq.push({ user.next_send_time, user.id });
                    } else {
                        user_pq.push({ user.next_send_time, user.id });
                    }
                } else {
                    user_pq.push({ user.next_send_time, user.id });
                }
            }
        }
        else {
            // 智能重试机制：如果找不到合适的NPU，分析原因并调整策略
            if (user.samples_left > 0) {
                long long remaining_time = user.e - time;

                // 如果剩余时间不足，尝试更激进的策略
                if (remaining_time <= 10) {
                    // 时间极其紧迫，尝试任何可用的NPU
                    user.next_send_time = time + 1;
                } else if (remaining_time <= 100) {
                    // 时间紧迫，减少等待时间
                    user.next_send_time = time + 1;
                } else {
                    // 正常情况，稍微延迟重试
                    user.next_send_time = time + 5;
                }

                // 只有在截止时间前才继续尝试
                if (user.next_send_time < user.e) {
                    user_pq.push({ user.next_send_time, user.id });
                }
            }
        }
    }

    for (auto& user : users) {
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
}