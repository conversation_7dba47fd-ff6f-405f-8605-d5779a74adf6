﻿  main.cpp
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(17,9): error C2065: “it”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(17,15): error C2065: “inference_time_cache”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(18,16): error C2065: “it”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(27,5): error C2065: “inference_time_cache”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(116,5): error C2059: 语法错误:“if”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(116,30): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(116,30): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(120,28): error C2065: “max_batch_mem”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(120,48): error C2065: “samples_left”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(124,47): error C2065: “k”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(124,51): error C2065: “remaining_time”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(125,5): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(125,5): error C2371: “theoretical_max_ll”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(124,15):
      参见“theoretical_max_ll”的声明
  
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(128,5): error C2059: 语法错误:“if”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(131,9): error C2059: 语法错误:“return”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(132,5): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(132,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(138,5): error C2059: 语法错误:“while”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(138,25): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(138,25): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(148,5): error C2059: 语法错误:“return”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(149,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(149,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(154,12): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(154,12): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(388,5): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(388,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(390,23): error C2065: “users”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(390,28): error C2059: 语法错误:“)”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(390,23): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(391,17): error C2065: “user”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(391,45): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,32): error C2065: “user”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,9): error C2059: 语法错误:“for”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,32): error C3927: "->": 非函数声明符后不允许尾随返回类型
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,36): error C3484: 语法错误: 返回类型前应为“->”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,37): error C3613: “->”后缺少返回类型(假定为“int”)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,32): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,37): error C2146: 语法错误: 缺少“;”(在标识符“requests”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,45): error C2059: 语法错误:“)”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,47): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(393,47): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(401,14): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(401,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(401,14): error C2059: 语法错误:“<<”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(402,5): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(402,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(405,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(405,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
